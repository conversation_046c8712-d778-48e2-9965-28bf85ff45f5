# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
dist-ssr/
build/

# 环境变量文件
.env
.env.local
.env.development
.env.production
.env.*.local

# 编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# 日志文件
logs/
*.log

# 临时文件
*.tmp
*.temp
*.backup
*.bak

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
.env
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/

# 测试覆盖率
coverage/
.nyc_output/

# 依赖目录
.pnp
.pnp.js

# 文档
README.md
docs/

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 部署脚本
deploy.sh
scripts/

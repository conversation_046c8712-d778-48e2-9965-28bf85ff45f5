# 医院邮件发送服务

一个基于FastAPI的医院邮件发送后端服务，为医院网站提供安全可靠的邮件发送功能。支持QQ邮箱SMTP发送，配置简单，部署方便。

## ✨ 主要特性

- ✅ **安全验证**：图形验证码防止恶意提交
- ✅ **文件上传**：支持多种格式的附件上传
- ✅ **邮件模板**：美观的HTML邮件模板
- ✅ **QQ邮箱支持**：使用QQ邮箱SMTP发送邮件
- ✅ **API文档**：自动生成的Swagger API文档
- ✅ **CORS支持**：支持跨域请求
- ✅ **日志记录**：详细的操作日志

## 🚀 快速开始

### 环境要求

- Python 3.8+
- pip 包管理器

### 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 配置环境变量

编辑 `.env` 文件，配置QQ邮箱信息：

```env
# QQ邮箱 SMTP配置（发送方）
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_qq_auth_code
SMTP_USE_TLS=true

# 邮件接收地址（医院邮箱）
TARGET_EMAIL=<EMAIL>
```

### 启动服务

```bash
# 开发环境
./start.sh

# 或手动启动
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

服务启动后，访问：
- API文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/api/health

## 📧 QQ邮箱SMTP配置

### 1. 开启IMAP/SMTP服务

1. 登录 [QQ邮箱](https://mail.qq.com)
2. 点击右上角「设置」→「账户」
3. 找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务」
4. 开启「IMAP/SMTP服务」

### 2. 获取授权码

1. 在IMAP/SMTP服务设置中，点击「生成授权码」
2. 按提示发送短信验证
3. 获得16位授权码（如：`abcdefghijklmnop`）

### 3. 配置授权码

将获得的授权码填入 `.env` 文件的 `SMTP_PASSWORD` 字段：

```env
SMTP_PASSWORD=abcdefghijklmnop
```

**注意**：使用授权码，不是QQ密码！

## 🔧 配置说明

### QQ邮箱SMTP配置

- **SMTP服务器**：smtp.qq.com
- **端口**：587（TLS加密）
- **认证**：使用QQ邮箱授权码

### 文件上传限制

- **最大文件大小**：5MB
- **支持的文件类型**：
  - 文档：PDF, DOC, DOCX, TXT
  - 图片：JPG, JPEG, PNG, GIF, BMP
  - 表格：XLS, XLSX
  - 演示：PPT, PPTX

### 验证码配置

- **验证码长度**：4位
- **过期时间**：10分钟
- **图片尺寸**：120x40像素

## 📚 API接口文档

### 验证码接口

#### 生成验证码
```http
GET /api/captcha/generate
```

**响应示例**：
```json
{
  "captcha_id": "uuid-string",
  "captcha_image": "data:image/png;base64,..."
}
```

#### 验证验证码
```http
POST /api/captcha/verify
```

**请求体**：
```json
{
  "captcha_id": "uuid-string",
  "captcha_code": "ABCD"
}
```

**响应示例**：
```json
{
  "valid": true,
  "message": "验证码正确"
}
```

### 邮件发送接口

#### 发送邮件
```http
POST /api/email/send
```

**请求体**（multipart/form-data）：
- `name`: 姓名
- `contact`: 联系方式
- `email`: 邮箱地址
- `address`: 详细地址
- `category`: 留言类别
- `subject`: 留言主题
- `content`: 留言内容
- `captcha_id`: 验证码ID
- `captcha_code`: 验证码
- `attachment`: 附件文件（可选）

**响应示例**：
```json
{
  "success": true,
  "message": "留言提交成功，我们会尽快回复您！"
}
```

### 健康检查接口

#### 服务状态
```http
GET /api/health
```

**响应示例**：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## 🌍 环境变量说明

| 变量名 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `SMTP_HOST` | SMTP服务器地址 | `smtp.qq.com` | ✅ |
| `SMTP_PORT` | SMTP端口 | `587` | ✅ |
| `SMTP_USERNAME` | QQ邮箱地址 | - | ✅ |
| `SMTP_PASSWORD` | QQ邮箱授权码 | - | ✅ |
| `SMTP_USE_TLS` | 是否使用TLS | `true` | ✅ |
| `TARGET_EMAIL` | 接收邮件的地址 | - | ✅ |
| `DEBUG` | 调试模式 | `false` | ❌ |
| `CORS_ORIGINS` | 允许的跨域来源 | `["*"]` | ❌ |
| `MAX_FILE_SIZE` | 最大文件大小（字节） | `5242880` | ❌ |
| `ALLOWED_FILE_TYPES` | 允许的文件类型 | 见配置文件 | ❌ |
| `CAPTCHA_LENGTH` | 验证码长度 | `4` | ❌ |
| `CAPTCHA_EXPIRE_MINUTES` | 验证码过期时间（分钟） | `10` | ❌ |
| `CAPTCHA_WIDTH` | 验证码图片宽度 | `120` | ❌ |
| `CAPTCHA_HEIGHT` | 验证码图片高度 | `40` | ❌ |

## 🚀 部署指南

### 生产环境部署

1. **克隆项目**：
   ```bash
   git clone <repository-url>
   cd hospital-email-service/backend
   ```

2. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**：
   ```bash
   cp .env.prod .env
   # 编辑 .env 文件，填入真实配置
   ```

4. **启动服务**：
   ```bash
   # 使用Gunicorn（推荐）
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

   # 或使用Uvicorn
   uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Nginx反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 故障排除

### 常见问题

1. **QQ邮箱认证失败**：
   - 确保已开启IMAP/SMTP服务
   - 使用授权码，不是QQ密码
   - 检查授权码格式（16位字符）

2. **文件上传失败**：
   - 检查文件大小是否超过5MB
   - 确认文件类型在允许列表中

3. **验证码显示异常**：
   - 确保Pillow库正确安装
   - 检查系统字体支持

4. **CORS错误**：
   - 检查 `CORS_ORIGINS` 配置
   - 确保前端域名在允许列表中

### 日志查看

服务运行时会输出详细日志，包括：
- 邮件发送状态
- 验证码生成记录
- 错误信息和堆栈跟踪

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请联系技术支持团队。

# 生产环境配置模板
# 请复制此文件为 .env 并填入真实配置信息

# QQ邮箱 SMTP配置（发送方）
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_qq_auth_code
SMTP_USE_TLS=true

# 邮件接收地址（医院邮箱）
TARGET_EMAIL=<EMAIL>

# 应用配置
DEBUG=false
CORS_ORIGINS=["https://your-domain.com","https://www.your-domain.com"]

# 文件上传配置
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=["pdf","doc","docx","txt","jpg","jpeg","png","gif","bmp","xls","xlsx","ppt","pptx"]

# 验证码配置
CAPTCHA_LENGTH=4
CAPTCHA_EXPIRE_MINUTES=10
CAPTCHA_WIDTH=120
CAPTCHA_HEIGHT=40

# 安全配置
# 在生产环境中，请设置具体的允许域名，不要使用 "*"
# CORS_ORIGINS=["https://hospital.example.com"]

# 性能配置
# 如果使用 Gunicorn，可以通过命令行参数设置 workers 数量
# gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 日志配置
# 在生产环境中建议配置日志轮转和持久化存储

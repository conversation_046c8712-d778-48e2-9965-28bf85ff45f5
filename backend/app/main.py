"""
FastAPI主应用
"""
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from app.config import settings
from app.routers import email

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG级别以获取更多信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 启动时检查依赖
def check_dependencies():
    """检查关键依赖是否正常"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        logger.info("✅ Pillow库导入成功")
    except ImportError as e:
        logger.error(f"❌ Pillow库导入失败: {e}")
        raise

    try:
        from app.services.captcha_service import captcha_service
        logger.info("✅ 验证码服务导入成功")
        # 测试生成验证码
        test_id, test_image = captcha_service.generate_captcha()
        logger.info(f"✅ 验证码测试生成成功: {test_id}")
    except Exception as e:
        logger.error(f"❌ 验证码服务测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise

# 在应用启动时检查依赖
check_dependencies()

# 创建FastAPI应用
app = FastAPI(
    title="医院邮件发送服务",
    description="为医院网站提供邮件发送功能的后端API服务",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(email.router)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "医院邮件发送服务",
        "version": "1.0.0",
        "status": "running"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error": "INTERNAL_SERVER_ERROR"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )

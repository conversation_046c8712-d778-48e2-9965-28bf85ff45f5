"""
邮件发送服务
"""
import smtplib
import logging
from typing import Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

from app.config import settings, get_smtp_config
from app.models import EmailTemplateData

logger = logging.getLogger(__name__)


class EmailService:
    """邮件发送服务类"""
    
    def __init__(self):
        self.smtp_config = get_smtp_config()
    
    def _create_smtp_connection(self) -> Optional[smtplib.SMTP]:
        """创建SMTP连接"""
        if not self.smtp_config:
            logger.error("未配置SMTP信息")
            return None
            
        try:
            smtp = smtplib.SMTP(self.smtp_config['host'], self.smtp_config['port'], timeout=30)
            if self.smtp_config['use_tls']:
                smtp.starttls()
            smtp.login(self.smtp_config['username'], self.smtp_config['password'])
            return smtp
        except Exception as e:
            logger.error(f"SMTP连接失败: {str(e)}")
            return None
    
    def _create_email_content(self, template_data: EmailTemplateData) -> str:
        """创建邮件HTML内容"""
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>在线留言通知</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #d32f2f; color: white; padding: 20px; text-align: center; }}
                .content {{ background-color: #f9f9f9; padding: 20px; }}
                .field {{ margin-bottom: 15px; }}
                .label {{ font-weight: bold; color: #d32f2f; }}
                .value {{ margin-top: 5px; padding: 8px; background-color: white; border-left: 3px solid #d32f2f; }}
                .footer {{ background-color: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }}
                .timestamp {{ color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>大连东海医院</h1>
                    <h2>在线留言通知</h2>
                </div>
                
                <div class="content">
                    <div class="field">
                        <div class="label">姓名：</div>
                        <div class="value">{template_data.name}</div>
                    </div>
                    
                    <div class="field">
                        <div class="label">联系方式：</div>
                        <div class="value">{template_data.contact}</div>
                    </div>
                    
                    <div class="field">
                        <div class="label">邮箱地址：</div>
                        <div class="value">{template_data.email}</div>
                    </div>
                    
                    <div class="field">
                        <div class="label">详细地址：</div>
                        <div class="value">{template_data.address}</div>
                    </div>
                    
                    <div class="field">
                        <div class="label">留言类别：</div>
                        <div class="value">{template_data.category}</div>
                    </div>
                    
                    <div class="field">
                        <div class="label">留言主题：</div>
                        <div class="value">{template_data.subject}</div>
                    </div>
                    
                    <div class="field">
                        <div class="label">留言内容：</div>
                        <div class="value">{template_data.content.replace(chr(10), '<br>')}</div>
                    </div>
                    
                    {f'<div class="field"><div class="label">附件信息：</div><div class="value">{template_data.attachment_info}</div></div>' if template_data.attachment_info else ''}
                    
                    <div class="timestamp">
                        提交时间：{template_data.timestamp}
                    </div>
                </div>
                
                <div class="footer">
                    <p>此邮件由大连东海医院在线留言系统自动发送</p>
                    <p>请及时处理用户留言，如有疑问请联系系统管理员</p>
                </div>
            </div>
        </body>
        </html>
        """
        return html_template
    
    def send_email(self, template_data: EmailTemplateData, attachment_file: Optional[bytes] = None, 
                   attachment_filename: Optional[str] = None) -> tuple[bool, str]:
        """发送邮件"""
        if not self.smtp_config:
            return False, "未配置SMTP信息"
        
        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.smtp_config['username']
            msg['To'] = settings.target_email
            msg['Subject'] = f"[大连东海医院] 新的在线留言 - {template_data.subject}"
            msg['Reply-To'] = template_data.email
            
            # 添加HTML内容
            html_content = self._create_email_content(template_data)
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))
            
            # 添加附件
            if attachment_file and attachment_filename:
                attachment = MIMEBase('application', 'octet-stream')
                attachment.set_payload(attachment_file)
                encoders.encode_base64(attachment)
                attachment.add_header(
                    'Content-Disposition',
                    f'attachment; filename="{attachment_filename}"'
                )
                msg.attach(attachment)
            
            # 发送邮件
            smtp = self._create_smtp_connection()
            if smtp:
                smtp.send_message(msg)
                smtp.quit()
                logger.info("邮件发送成功")
                return True, "邮件发送成功"
            else:
                return False, "SMTP连接失败"
                
        except Exception as e:
            error_msg = f"邮件发送失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg


# 创建全局邮件服务实例
email_service = EmailService()

"""
邮件相关API路由
"""
import logging
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import JSONResponse

from app.models import (
    EmailRequest, EmailResponse, CaptchaResponse, 
    CaptchaVerifyRequest, CaptchaVerifyResponse, 
    EmailTemplateData, FileInfo
)
from app.services.email_service import email_service
from app.services.captcha_service import captcha_service
from app.config import settings, validate_file_type, format_file_size

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["email"])


@router.get("/captcha/generate", response_model=CaptchaResponse)
async def generate_captcha():
    """生成验证码"""
    try:
        captcha_id, captcha_image = captcha_service.generate_captcha()
        return CaptchaResponse(
            captcha_id=captcha_id,
            captcha_image=captcha_image
        )
    except Exception as e:
        logger.error(f"生成验证码失败: {str(e)}")
        raise HTTPException(status_code=500, detail="生成验证码失败")


@router.post("/captcha/verify", response_model=CaptchaVerifyResponse)
async def verify_captcha(request: CaptchaVerifyRequest):
    """验证验证码"""
    try:
        is_valid = captcha_service.verify_captcha(request.captcha_id, request.captcha_code)
        return CaptchaVerifyResponse(
            valid=is_valid,
            message="验证码正确" if is_valid else "验证码错误或已过期"
        )
    except Exception as e:
        logger.error(f"验证验证码失败: {str(e)}")
        raise HTTPException(status_code=500, detail="验证验证码失败")


@router.post("/email/send", response_model=EmailResponse)
async def send_email(
    name: str = Form(...),
    contact: str = Form(...),
    email: str = Form(...),
    address: str = Form(...),
    category: str = Form(...),
    subject: str = Form(...),
    content: str = Form(...),
    captcha_id: str = Form(...),
    captcha_code: str = Form(...),
    attachment: Optional[UploadFile] = File(None)
):
    """发送邮件"""
    try:
        # 验证验证码
        if not captcha_service.verify_captcha(captcha_id, captcha_code):
            return EmailResponse(
                success=False,
                message="验证码错误或已过期",
                error="CAPTCHA_INVALID"
            )
        
        # 处理附件
        attachment_info = ""
        attachment_file = None
        attachment_filename = None
        
        if attachment and attachment.filename:
            # 验证文件类型
            if not validate_file_type(attachment.filename):
                return EmailResponse(
                    success=False,
                    message="不支持的文件类型",
                    error="FILE_TYPE_NOT_ALLOWED"
                )
            
            # 读取文件内容
            attachment_content = await attachment.read()
            
            # 验证文件大小
            if len(attachment_content) > settings.max_file_size:
                return EmailResponse(
                    success=False,
                    message=f"文件大小超过限制（最大{format_file_size(settings.max_file_size)}）",
                    error="FILE_SIZE_TOO_LARGE"
                )
            
            attachment_file = attachment_content
            attachment_filename = attachment.filename
            attachment_info = f"附件：{attachment.filename} ({format_file_size(len(attachment_content))})"
        
        # 创建邮件模板数据
        template_data = EmailTemplateData(
            name=name,
            contact=contact,
            email=email,
            address=address,
            category=category,
            subject=subject,
            content=content,
            attachment_info=attachment_info,
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        # 发送邮件
        success, message = email_service.send_email(
            template_data=template_data,
            attachment_file=attachment_file,
            attachment_filename=attachment_filename
        )
        
        if success:
            return EmailResponse(
                success=True,
                message="留言提交成功，我们会尽快回复您！"
            )
        else:
            return EmailResponse(
                success=False,
                message="留言提交失败，请稍后重试",
                error="EMAIL_SEND_FAILED"
            )
    
    except Exception as e:
        logger.error(f"发送邮件异常: {str(e)}")
        return EmailResponse(
            success=False,
            message="系统错误，请稍后重试",
            error="SYSTEM_ERROR"
        )


@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: hospital-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - hospital-network

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: hospital-backend
    ports:
      - "8000:8000"
    environment:
      - SMTP_HOST=${SMTP_HOST:-smtp.qq.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_USE_TLS=${SMTP_USE_TLS:-true}
      - TARGET_EMAIL=${TARGET_EMAIL}
      - DEBUG=${DEBUG:-false}
      - CORS_ORIGINS=["http://localhost", "http://localhost:80"]
    volumes:
      - ./backend/.env:/app/.env:ro
    restart: unless-stopped
    networks:
      - hospital-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  hospital-network:
    driver: bridge

volumes:
  backend-data:

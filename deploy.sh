#!/bin/bash

# 医院网站 Docker 部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "医院网站 Docker 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV        部署环境 (dev|prod) [默认: dev]"
    echo "  -p, --pull           部署前拉取最新代码"
    echo "  -c, --clean          清理旧的容器和镜像"
    echo "  -b, --build          强制重新构建镜像"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -e dev            部署开发环境"
    echo "  $0 -e prod -p -c     部署生产环境，拉取代码并清理"
    echo "  $0 -b                强制重新构建并部署"
}

# 默认参数
ENVIRONMENT="dev"
PULL_CODE=false
CLEAN_OLD=false
FORCE_BUILD=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--pull)
            PULL_CODE=true
            shift
            ;;
        -c|--clean)
            CLEAN_OLD=true
            shift
            ;;
        -b|--build)
            FORCE_BUILD=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    print_error "无效的环境参数: $ENVIRONMENT (只支持 dev 或 prod)"
    exit 1
fi

print_message "开始部署医院网站 - 环境: $ENVIRONMENT"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 拉取最新代码
if [ "$PULL_CODE" = true ]; then
    print_step "拉取最新代码..."
    git pull origin main || {
        print_warning "Git pull 失败，继续部署..."
    }
fi

# 检查环境变量文件
print_step "检查环境变量文件..."
if [ "$ENVIRONMENT" = "prod" ]; then
    if [ ! -f ".env.prod" ]; then
        print_error "生产环境变量文件 .env.prod 不存在"
        exit 1
    fi
    if [ ! -f "backend/.env.prod" ]; then
        print_error "后端生产环境变量文件 backend/.env.prod 不存在"
        exit 1
    fi
    print_message "生产环境变量文件检查通过"
else
    if [ ! -f ".env.local" ]; then
        print_warning "开发环境变量文件 .env.local 不存在，将使用 .env.example"
        cp .env.example .env.local 2>/dev/null || true
    fi
    if [ ! -f "backend/.env" ]; then
        print_warning "后端环境变量文件 backend/.env 不存在"
    fi
fi

# 清理旧容器和镜像
if [ "$CLEAN_OLD" = true ]; then
    print_step "清理旧的容器和镜像..."
    
    # 停止并删除容器
    docker-compose -f docker-compose.yml down --remove-orphans 2>/dev/null || true
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true
    fi
    
    # 删除相关镜像
    docker images | grep hospital | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    
    # 清理未使用的资源
    docker system prune -f
    
    print_message "清理完成"
fi

# 构建和部署
print_step "构建和部署应用..."

if [ "$ENVIRONMENT" = "prod" ]; then
    # 生产环境部署
    print_message "部署生产环境..."
    
    if [ "$FORCE_BUILD" = true ]; then
        docker-compose -f docker-compose.prod.yml build --no-cache
    else
        docker-compose -f docker-compose.prod.yml build
    fi
    
    docker-compose -f docker-compose.prod.yml up -d
    
else
    # 开发环境部署
    print_message "部署开发环境..."
    
    if [ "$FORCE_BUILD" = true ]; then
        docker-compose build --no-cache
    else
        docker-compose build
    fi
    
    docker-compose up -d
fi

# 等待服务启动
print_step "等待服务启动..."
sleep 10

# 检查服务状态
print_step "检查服务状态..."

if [ "$ENVIRONMENT" = "prod" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
else
    COMPOSE_FILE="docker-compose.yml"
fi

# 检查容器状态
CONTAINERS=$(docker-compose -f $COMPOSE_FILE ps -q)
for container in $CONTAINERS; do
    if [ "$(docker inspect -f '{{.State.Running}}' $container)" = "true" ]; then
        CONTAINER_NAME=$(docker inspect -f '{{.Name}}' $container | sed 's/\///')
        print_message "容器 $CONTAINER_NAME 运行正常"
    else
        CONTAINER_NAME=$(docker inspect -f '{{.Name}}' $container | sed 's/\///')
        print_error "容器 $CONTAINER_NAME 启动失败"
        docker logs $container --tail 20
    fi
done

# 健康检查
print_step "执行健康检查..."
sleep 5

# 检查后端健康状态
if curl -f http://localhost:8000/api/health &>/dev/null; then
    print_message "后端服务健康检查通过"
else
    print_warning "后端服务健康检查失败，请检查日志"
fi

# 检查前端服务
if curl -f http://localhost/ &>/dev/null; then
    print_message "前端服务健康检查通过"
else
    print_warning "前端服务健康检查失败，请检查日志"
fi

# 显示部署信息
print_message "部署完成！"
echo ""
echo "服务访问地址:"
echo "  前端: http://localhost"
echo "  后端API: http://localhost:8000"
echo "  API文档: http://localhost:8000/docs"
echo ""
echo "查看日志:"
echo "  docker-compose -f $COMPOSE_FILE logs -f"
echo ""
echo "停止服务:"
echo "  docker-compose -f $COMPOSE_FILE down"

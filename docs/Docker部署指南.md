# 🐳 Docker 部署指南

本指南详细介绍如何在 Linux 服务器上使用 Docker 部署医院网站项目。

## 📋 部署架构

```
┌─────────────────┐    ┌─────────────────┐
│   Nginx (前端)   │    │  FastAPI (后端)  │
│   Port: 80      │    │   Port: 8000    │
│   React + TS    │◄──►│   Python 3.9    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                   │
            Docker Network
```

## 🚀 快速部署

### 1. 环境要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间

### 2. 安装 Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 3. 克隆项目

```bash
git clone <your-repository-url>
cd hospital-website
```

### 4. 配置环境变量

#### 前端环境变量
```bash
# 复制并编辑前端环境变量
cp .env.example .env.prod

# 编辑 .env.prod
nano .env.prod
```

`.env.prod` 示例内容：
```env
# 后端API配置
VITE_API_BASE_URL=http://your-domain.com:8000/api

# 百度地图API密钥
VITE_BAIDU_MAP_AK=your_baidu_map_api_key

# 医院信息
VITE_HOSPITAL_NAME=大连东海医院
VITE_HOSPITAL_EMAIL=<EMAIL>
```

#### 后端环境变量
```bash
# 复制并编辑后端环境变量
cp backend/.env.example backend/.env.prod

# 编辑后端环境变量
nano backend/.env.prod
```

`backend/.env.prod` 示例内容：
```env
# QQ邮箱 SMTP配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_qq_auth_code
SMTP_USE_TLS=true

# 邮件接收地址
TARGET_EMAIL=<EMAIL>

# 应用配置
DEBUG=false
CORS_ORIGINS=["http://your-domain.com", "https://your-domain.com"]
```

### 5. 一键部署

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 开发环境部署
./deploy.sh -e dev

# 生产环境部署
./deploy.sh -e prod -p -c
```

## 📝 详细部署步骤

### 方法一：使用部署脚本（推荐）

```bash
# 查看帮助
./deploy.sh -h

# 生产环境完整部署
./deploy.sh -e prod -p -c -b

# 参数说明：
# -e prod: 生产环境
# -p: 拉取最新代码
# -c: 清理旧容器
# -b: 强制重新构建
```

### 方法二：手动部署

#### 开发环境
```bash
# 构建并启动服务
docker-compose build
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 生产环境
```bash
# 构建并启动生产环境
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps
```

## 🔧 服务管理

### 常用命令

```bash
# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v

# 更新服务
docker-compose pull
docker-compose up -d
```

### 服务访问

- **前端网站**: http://your-server-ip
- **后端API**: http://your-server-ip:8000
- **API文档**: http://your-server-ip:8000/docs

## 🔍 健康检查

### 自动健康检查
Docker Compose 配置了自动健康检查：

```bash
# 查看健康状态
docker-compose ps

# 手动健康检查
curl http://localhost:8000/api/health
curl http://localhost/
```

### 监控脚本
```bash
#!/bin/bash
# health-check.sh

echo "检查前端服务..."
if curl -f http://localhost/ &>/dev/null; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

echo "检查后端服务..."
if curl -f http://localhost:8000/api/health &>/dev/null; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
fi
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8000/tcp  # 仅开发环境

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 2. SSL 证书配置
```bash
# 创建 SSL 目录
mkdir -p ssl

# 复制证书文件
cp your-cert.pem ssl/
cp your-key.pem ssl/

# 更新 nginx 配置以支持 HTTPS
```

### 3. 环境变量安全
```bash
# 设置文件权限
chmod 600 .env.prod
chmod 600 backend/.env.prod

# 确保不被 Git 跟踪
echo ".env.prod" >> .gitignore
echo "backend/.env.prod" >> .gitignore
```

## 📊 性能优化

### 1. 资源限制
在 `docker-compose.prod.yml` 中添加：

```yaml
services:
  frontend:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
  
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
```

### 2. 日志管理
```yaml
services:
  backend:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🔄 更新部署

### 代码更新
```bash
# 拉取最新代码
git pull origin main

# 重新部署
./deploy.sh -e prod -b
```

### 配置更新
```bash
# 更新环境变量后重启
docker-compose restart

# 或重新部署
docker-compose down
docker-compose up -d
```

## 🐛 故障排除

### 常见问题

1. **容器启动失败**
```bash
# 查看详细日志
docker-compose logs [service_name]

# 检查配置文件
docker-compose config
```

2. **端口冲突**
```bash
# 查看端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8000

# 修改端口映射
```

3. **权限问题**
```bash
# 检查文件权限
ls -la .env.prod
ls -la backend/.env.prod

# 修复权限
chmod 600 .env.prod
```

4. **网络问题**
```bash
# 检查 Docker 网络
docker network ls
docker network inspect hospital_hospital-network
```

### 日志分析
```bash
# 实时查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 查看最近的错误
docker-compose logs --tail=50 backend | grep ERROR
```

## 📈 监控和维护

### 1. 系统监控
```bash
# 查看资源使用
docker stats

# 查看磁盘使用
docker system df

# 清理未使用资源
docker system prune -f
```

### 2. 备份策略
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/hospital-website"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz .env.prod backend/.env.prod

# 备份数据库（如果有）
# docker exec hospital-db mysqldump -u root -p database > $BACKUP_DIR/db_$DATE.sql

echo "备份完成: $BACKUP_DIR"
```

### 3. 自动化维护
```bash
# 添加到 crontab
crontab -e

# 每天凌晨 2 点备份
0 2 * * * /path/to/backup.sh

# 每周清理 Docker 资源
0 3 * * 0 docker system prune -f
```

通过以上配置，您就可以在 Linux 服务器上成功部署医院网站项目了！🎉

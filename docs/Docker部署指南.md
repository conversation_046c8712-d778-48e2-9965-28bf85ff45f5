# 🐳 Linux 服务器 Docker 部署指南

本指南介绍如何在 Linux 服务器上使用 Docker 部署医院网站项目的操作步骤。

## 📋 项目架构

```
┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │
│   Port: 3000    │    │   Port: 8000    │
│   Nginx 代理     │◄──►│   Python 3.9    │
└─────────────────┘    └─────────────────┘
```

## 🚀 服务器部署步骤

### 1. 环境要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间

### 2. 安装 Docker 和 Docker Compose

```bash
# Ubuntu/Debian 系统
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录或执行以下命令使用户组生效
newgrp docker

# 验证安装
docker --version
docker-compose --version
```

### 3. 上传项目到服务器

```bash
# 方法1: 使用 Git 克隆
git clone <your-repository-url>
cd hospital-website

# 方法2: 使用 SCP 上传
# 在本地执行：
scp -r /path/to/project user@server-ip:/home/<USER>/hospital-website
```

### 4. 配置环境变量

#### 前端环境变量
```bash
# 复制环境变量模板
cp .env.example .env.prod

# 编辑前端环境变量
nano .env.prod
```

编辑内容示例：
```env
# 后端API配置 (改为服务器IP或域名)
VITE_API_BASE_URL=http://your-server-ip:8000/api

# 百度地图API密钥
VITE_BAIDU_MAP_AK=your_baidu_map_api_key

# 医院信息
VITE_HOSPITAL_NAME=大连东海医院
VITE_HOSPITAL_EMAIL=<EMAIL>
```

#### 后端环境变量
```bash
# 进入后端目录
cd backend

# 复制环境变量模板
cp .env.example .env

# 编辑后端环境变量
nano .env
```

编辑内容示例：
```env
# QQ邮箱 SMTP配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_qq_auth_code
SMTP_USE_TLS=true

# 邮件接收地址
TARGET_EMAIL=<EMAIL>

# 应用配置
DEBUG=false
CORS_ORIGINS=["http://your-server-ip", "https://your-server-ip"]
```

### 5. 直接使用 Docker 命令部署（无需配置文件）

#### 方法一：分别构建前后端容器

**构建并运行后端服务：**
```bash
# 构建后端镜像
docker build -t hospital-backend -f - backend <<EOF
FROM python:3.9-slim
WORKDIR /app
RUN apt-get update && apt-get install -y gcc && rm -rf /var/lib/apt/lists/*
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# 运行后端容器
docker run -d \
  --name hospital-backend \
  -p 8000:8000 \
  -v $(pwd)/backend/.env:/app/.env:ro \
  --restart unless-stopped \
  hospital-backend
```

**构建并运行前端服务：**
```bash
# 构建前端镜像
docker build -t hospital-frontend -f - . <<EOF
FROM node:18-alpine AS builder
WORKDIR /app
RUN npm install -g pnpm
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile
COPY . .
COPY .env.prod .env
RUN pnpm build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
RUN echo 'events { worker_connections 1024; }
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;
        location / { try_files \$uri \$uri/ /index.html; }
        location /assets/ { expires 1y; add_header Cache-Control "public, immutable"; }
    }
}' > /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF

# 运行前端容器
docker run -d \
  --name hospital-frontend \
  -p 80:80 \
  --link hospital-backend:backend \
  --restart unless-stopped \
  hospital-frontend
```

#### 方法二：使用 Docker 网络（推荐）

```bash
# 创建自定义网络
docker network create hospital-network

# 运行后端服务
docker run -d \
  --name hospital-backend \
  --network hospital-network \
  -p 8000:8000 \
  -v $(pwd)/backend/.env:/app/.env:ro \
  --restart unless-stopped \
  python:3.9-slim bash -c "
    apt-get update && apt-get install -y gcc && rm -rf /var/lib/apt/lists/* &&
    cd /app &&
    pip install fastapi uvicorn python-multipart python-dotenv pydantic pydantic-settings Pillow email-validator aiofiles jinja2 &&
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
  " \
  -v $(pwd)/backend:/app

# 运行前端服务（使用预构建的 nginx 镜像）
docker run -d \
  --name hospital-frontend \
  --network hospital-network \
  -p 80:80 \
  --restart unless-stopped \
  -v $(pwd)/dist:/usr/share/nginx/html:ro \
  nginx:alpine

# 先在本地构建前端
cp .env.prod .env
pnpm install
pnpm build
```

#### 方法三：最简单的单命令部署

```bash
# 一键启动后端（开发模式）
docker run -d \
  --name hospital-backend \
  -p 8000:8000 \
  -v $(pwd)/backend:/app \
  -w /app \
  --restart unless-stopped \
  python:3.9-slim bash -c "
    pip install fastapi uvicorn python-multipart python-dotenv pydantic pydantic-settings Pillow email-validator aiofiles jinja2 &&
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
  "

# 本地构建前端并启动 nginx
cp .env.prod .env && pnpm install && pnpm build
docker run -d \
  --name hospital-frontend \
  -p 80:80 \
  -v $(pwd)/dist:/usr/share/nginx/html:ro \
  --restart unless-stopped \
  nginx:alpine
```

### 6. 查看和管理服务

```bash
# 查看运行状态
docker ps

# 查看日志
docker logs hospital-backend
docker logs hospital-frontend

# 重启服务
docker restart hospital-backend
docker restart hospital-frontend

# 停止服务
docker stop hospital-backend hospital-frontend

# 删除容器
docker rm hospital-backend hospital-frontend
```

## 🔧 服务管理

### 常用命令

```bash
# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build
```

### 服务访问

部署成功后，可通过以下地址访问：
- **前端网站**: http://your-server-ip
- **后端API**: http://your-server-ip:8000
- **API文档**: http://your-server-ip:8000/docs

## 🔍 验证部署

### 检查服务状态
```bash
# 检查容器运行状态
docker-compose ps

# 检查前端服务
curl http://localhost/

# 检查后端服务
curl http://localhost:8000/api/health
```

### 查看日志排错
```bash
# 查看所有服务日志
docker-compose logs

# 查看最近的错误日志
docker-compose logs --tail=50 | grep -i error
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. 环境变量安全
```bash
# 设置文件权限
chmod 600 .env.prod
chmod 600 backend/.env

# 确保敏感文件不被 Git 跟踪
echo ".env.prod" >> .gitignore
echo "backend/.env" >> .gitignore
```

## 🔄 更新部署

### 代码更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose down
docker-compose up -d --build
```

### 配置更新
```bash
# 更新环境变量后重启
docker-compose restart

# 或重新部署
docker-compose down
docker-compose up -d
```

## 🐛 故障排除

### 常见问题

1. **容器启动失败**
```bash
# 查看详细日志
docker-compose logs backend
docker-compose logs frontend

# 检查配置文件语法
docker-compose config
```

2. **端口冲突**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :8000

# 如果端口被占用，停止占用进程或修改端口映射
```

3. **权限问题**
```bash
# 检查文件权限
ls -la .env.prod
ls -la backend/.env

# 修复权限
chmod 600 .env.prod
chmod 600 backend/.env
```

4. **网络连接问题**
```bash
# 检查容器网络
docker network ls
docker inspect bridge
```

### 日志分析
```bash
# 实时查看所有服务日志
docker-compose logs -f

# 查看最近的错误日志
docker-compose logs --tail=100 | grep -i error
```

## 📈 维护和监控

### 系统监控
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 清理未使用资源
docker system prune -f
```

### 定期维护
```bash
# 每周执行一次清理
docker system prune -f

# 查看日志文件大小
du -sh /var/lib/docker/containers/*/

# 重启服务（如需要）
docker-compose restart
```

## 📝 部署完成检查清单

- [ ] Docker 和 Docker Compose 已安装
- [ ] 项目代码已上传到服务器
- [ ] 前端环境变量 `.env.prod` 已配置
- [ ] 后端环境变量 `backend/.env` 已配置
- [ ] Docker 配置文件已创建
- [ ] 防火墙端口已开放
- [ ] 服务已成功启动
- [ ] 前端网站可正常访问
- [ ] 后端API可正常访问
- [ ] 邮件发送功能已测试

完成以上步骤后，您的医院网站就成功部署在 Linux 服务器上了！🎉

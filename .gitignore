# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.production
.env.development
.env.*.local
.env.prod

# Backend Python files
backend/.env
backend/.env.prod
backend/.env.local
backend/.env.development
backend/.env.production
backend/__pycache__/
backend/**/__pycache__/
backend/*.pyc
backend/**/*.pyc
backend/.pytest_cache/
backend/venv/
backend/env/
backend/.venv/

# EmailJS related sensitive files (legacy)
**/emailjs-config.json
**/email-secrets.*

# Backend temporary files
backend/test_*.py
backend/*_test.py
backend/debug_*.py
backend/temp_*.py
backend/*.log
backend/logs/

# Temporary and backup files
*.tmp
*.temp
*.backup
*.bak

# OS generated files
Thumbs.db
.DS_Store


生产环境部署说明.md
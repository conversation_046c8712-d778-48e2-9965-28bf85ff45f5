version: '3.8'

services:
  # 前端服务 - 生产环境
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: hospital-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - hospital-network
    volumes:
      # SSL 证书挂载（如果有的话）
      - ./ssl:/etc/nginx/ssl:ro

  # 后端服务 - 生产环境
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: hospital-backend-prod
    ports:
      - "8000:8000"
    environment:
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_USE_TLS=${SMTP_USE_TLS}
      - TARGET_EMAIL=${TARGET_EMAIL}
      - DEBUG=false
      - CORS_ORIGINS=["https://yourdomain.com", "http://yourdomain.com"]
    env_file:
      - ./backend/.env.prod
    restart: unless-stopped
    networks:
      - hospital-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx 反向代理（可选，用于更复杂的配置）
  nginx-proxy:
    image: nginx:alpine
    container_name: hospital-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - hospital-network

networks:
  hospital-network:
    driver: bridge

volumes:
  backend-data:
  ssl-certs:
